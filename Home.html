<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经销商年会工牌分析报告</title>
    <script src="https://cdn.tailwindcss.com/3.3.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="data.js"></script>
    <style>
        :root {
            --primary-100:#0077C2;
            --primary-200:#59a5f5;
            --primary-300:#c8ffff;
            --accent-100:#00BFFF;
            --accent-200:#00619a;
            --text-100:#333333;
            --text-200:#5c5c5c;
            --bg-100:#FFFFFF;
            --bg-200:#f5f5f5;
            --bg-300:#cccccc;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-100);
            color: var(--text-100);
        }
        .glass-card {
            background: var(--bg-200);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(224, 224, 224, 0.2);
        }
        .chart-container {
            height: 300px;
            width: 100%;
            position: relative;
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="min-h-screen pb-20">
    <!-- 顶部导航区 -->
    <header class="sticky top-0 z-50 bg-[var(--bg-200)] shadow-sm">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-bold text-[var(--text-100)]">
                    <i class="fas fa-comments mr-2"></i>经销商年会工牌分析报告
                </h1>
            </div>
            
            <!-- 核心指标卡片 -->
            <div class="mt-4 grid grid-cols-3 gap-3">
                <div class="glass-card p-3 text-center">
                    <p class="text-sm text-[var(--text-100)]">话题参与度</p>
                    <p class="text-2xl font-bold text-[var(--accent-200)]"><span id="topicEngagement">85</span><span class="text-sm">%</span></p>
                </div>
                <div class="glass-card p-3 text-center">
                    <p class="text-sm text-[var(--text-100)]">平均满意度</p>
                    <p class="text-2xl font-bold text-[var(--accent-200)]"><span id="averageSatisfaction">78</span><span class="text-sm">%</span></p>
                </div>
                <div class="glass-card p-3 text-center">
                    <p class="text-sm text-[var(--text-100)]">高频关键词</p>
                    <p class="text-2xl font-bold text-[var(--accent-200)]">+<span id="topKeywordsGrowth">23</span><span class="text-sm">%</span></p>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 mt-6 space-y-6">
        <!-- 上层图表区域 -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-6 animate-fade-in">
            <!-- 经销商满意度分析（柱状图） -->
            <div class="glass-card p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-chart-bar text-[var(--primary-100)] mr-2"></i>经销商满意度分析
                </h3>
                <div class="chart-container" style="height: 350px;">
                    <canvas id="satisfactionChart"></canvas>
                </div>
            </div>
            
            <!-- 功能评价对比（雷达图） -->
            <div class="glass-card p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-chart-line text-[var(--primary-100)] mr-2"></i>功能评价对比
                </h3>
                <div class="filter-container mb-3">
                    <select id="modelFilter" class="px-3 py-1 border rounded text-sm">
                        <option value="all">所有车型</option>
                        <option value="our">我司产品</option>
                        <option value="competitor1">竞品A</option>
                        <option value="competitor2">竞品B</option>
                        <option value="competitor3">竞品C</option>
                    </select>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="radarChart"></canvas>
                </div>
            </div>
            
            <!-- 高频关键词云 -->
            <div class="glass-card p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-cloud text-[var(--primary-100)] mr-2"></i>高频关键词云
                </h3>
                <div class="chart-container" style="height: 350px;">
                    <canvas id="wordCloudChart"></canvas>
                </div>
            </div>
        </section>

        <!-- 下层图表区域 -->
        <section class="grid grid-cols-1 md:grid-cols-2 gap-6 animate-fade-in">
            <!-- 经销商认可&抗拒点 -->
            <div class="glass-card p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-thumbs-up text-[var(--primary-100)] mr-2"></i>经销商认可&抗拒点
                </h3>
                <div class="chart-container" style="height: 400px;">
                    <canvas id="sentimentChart"></canvas>
                </div>
                <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                    <div class="bg-green-50 p-3 rounded">
                        <div class="text-green-600 font-bold text-xl"><span id="positivePercentage">68</span>%</div>
                        <div class="text-sm text-gray-600">认可度</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                        <div class="text-gray-600 font-bold text-xl"><span id="neutralPercentage">22</span>%</div>
                        <div class="text-sm text-gray-600">中性</div>
                    </div>
                    <div class="bg-red-50 p-3 rounded">
                        <div class="text-red-600 font-bold text-xl"><span id="negativePercentage">10</span>%</div>
                        <div class="text-sm text-gray-600">抗拒度</div>
                    </div>
                </div>
            </div>
            
            <!-- 经销商TOP热议话题 -->
            <div class="glass-card p-6">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-fire text-[var(--primary-100)] mr-2"></i>经销商TOP热议话题
                </h3>
                <div class="chart-container" style="height: 400px;">
                    <canvas id="topIssuesChart"></canvas>
                </div>
                <div class="mt-4">
                    <div class="text-sm text-gray-600 mb-2">话题热度趋势</div>
                    <div class="chart-container" style="height: 100px;">
                        <canvas id="topicTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <script>
        // 全局变量
        let charts = {};
        
        // 模拟数据
        const mockData = {
            satisfaction: {
                labels: ['产品质量', '价格合理性', '服务支持', '品牌认知', '销售政策'],
                data: [85, 72, 78, 88, 65]
            },
            radar: {
                labels: ['性能', '外观', '内饰', '配置', '价格', '服务'],
                datasets: [
                    {
                        label: '我司产品',
                        data: [85, 78, 82, 75, 70, 88],
                        borderColor: 'rgba(0, 119, 194, 1)',
                        backgroundColor: 'rgba(0, 119, 194, 0.2)'
                    },
                    {
                        label: '竞品A',
                        data: [75, 85, 75, 80, 85, 70],
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)'
                    }
                ]
            },
            sentiment: {
                positive: 68,
                neutral: 22,
                negative: 10
            },
            topIssues: {
                labels: ['价格政策', '产品配置', '售后服务', '市场支持', '培训需求'],
                data: [45, 38, 32, 28, 25]
            },
            wordCloud: [
                {text: '质量', size: 40}, {text: '价格', size: 35}, {text: '服务', size: 30},
                {text: '配置', size: 28}, {text: '外观', size: 25}, {text: '性能', size: 22},
                {text: '品牌', size: 20}, {text: '政策', size: 18}, {text: '支持', size: 15},
                {text: '培训', size: 12}
            ]
        };

        // 初始化所有图表
        function initCharts() {
            initSatisfactionChart();
            initRadarChart();
            initSentimentChart();
            initTopIssuesChart();
            initWordCloudChart();
            initTopicTrendChart();
        }

        // 经销商满意度分析（柱状图）
        function initSatisfactionChart() {
            const ctx = document.getElementById('satisfactionChart').getContext('2d');
            charts.satisfaction = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: mockData.satisfaction.labels,
                    datasets: [{
                        label: '满意度评分',
                        data: mockData.satisfaction.data,
                        backgroundColor: [
                            'rgba(0, 119, 194, 0.8)',
                            'rgba(89, 165, 245, 0.8)',
                            'rgba(0, 191, 255, 0.8)',
                            'rgba(0, 119, 194, 0.6)',
                            'rgba(89, 165, 245, 0.6)'
                        ],
                        borderColor: [
                            'rgba(0, 119, 194, 1)',
                            'rgba(89, 165, 245, 1)',
                            'rgba(0, 191, 255, 1)',
                            'rgba(0, 119, 194, 1)',
                            'rgba(89, 165, 245, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 功能评价对比（雷达图）
        function initRadarChart() {
            const ctx = document.getElementById('radarChart').getContext('2d');
            charts.radar = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: mockData.radar.labels,
                    datasets: mockData.radar.datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                stepSize: 20
                            }
                        }
                    }
                }
            });
        }

        // 经销商认可&抗拒点（饼图）
        function initSentimentChart() {
            const ctx = document.getElementById('sentimentChart').getContext('2d');
            charts.sentiment = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['认可', '中性', '抗拒'],
                    datasets: [{
                        data: [mockData.sentiment.positive, mockData.sentiment.neutral, mockData.sentiment.negative],
                        backgroundColor: [
                            'rgba(40, 167, 69, 0.8)',
                            'rgba(108, 117, 125, 0.8)',
                            'rgba(220, 53, 69, 0.8)'
                        ],
                        borderColor: [
                            'rgba(40, 167, 69, 1)',
                            'rgba(108, 117, 125, 1)',
                            'rgba(220, 53, 69, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 经销商TOP热议话题（水平柱状图）
        function initTopIssuesChart() {
            const ctx = document.getElementById('topIssuesChart').getContext('2d');
            charts.topIssues = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: mockData.topIssues.labels,
                    datasets: [{
                        label: '讨论热度',
                        data: mockData.topIssues.data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 高频关键词云（模拟词云效果）
        function initWordCloudChart() {
            const ctx = document.getElementById('wordCloudChart').getContext('2d');
            const canvas = ctx.canvas;

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 设置字体和颜色
            const colors = ['#0077C2', '#59a5f5', '#00BFFF', '#00619a', '#c8ffff'];

            mockData.wordCloud.forEach((word, index) => {
                const x = Math.random() * (canvas.width - 100) + 50;
                const y = Math.random() * (canvas.height - 50) + 25;
                const fontSize = word.size;

                ctx.font = `${fontSize}px Arial`;
                ctx.fillStyle = colors[index % colors.length];
                ctx.textAlign = 'center';
                ctx.fillText(word.text, x, y);
            });
        }

        // 话题热度趋势（小型线图）
        function initTopicTrendChart() {
            const ctx = document.getElementById('topicTrendChart').getContext('2d');
            charts.topicTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '话题热度',
                        data: [20, 35, 45, 38, 42, 48],
                        borderColor: 'rgba(0, 119, 194, 1)',
                        backgroundColor: 'rgba(0, 119, 194, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false,
                            beginAtZero: true
                        }
                    },
                    elements: {
                        point: {
                            radius: 2
                        }
                    }
                }
            });
        }

        // 车型筛选功能
        document.getElementById('modelFilter').addEventListener('change', function(e) {
            const selectedModel = e.target.value;
            updateRadarChart(selectedModel);
        });

        function updateRadarChart(model) {
            let datasets = [];

            switch(model) {
                case 'our':
                    datasets = [mockData.radar.datasets[0]];
                    break;
                case 'competitor1':
                    datasets = [mockData.radar.datasets[1]];
                    break;
                case 'all':
                default:
                    datasets = mockData.radar.datasets;
                    break;
            }

            charts.radar.data.datasets = datasets;
            charts.radar.update();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            Object.values(charts).forEach(chart => {
                if (chart && typeof chart.resize === 'function') {
                    chart.resize();
                }
            });

            // 重新绘制词云
            setTimeout(() => {
                initWordCloudChart();
            }, 100);
        });
    </script>
</body>
</html>
