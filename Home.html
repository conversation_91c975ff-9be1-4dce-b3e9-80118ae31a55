<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经销商年会工牌分析报告</title>
    <script src="https://cdn.tailwindcss.com/3.3.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/11.5.0/mermaid.min.js"></script>
    <script src="data.js"></script>
    <style>
        :root {
            --primary-100:#0077C2;
            --primary-200:#59a5f5;
            --primary-300:#c8ffff;
            --accent-100:#00BFFF;
            --accent-200:#00619a;
            --text-100:#333333;
            --text-200:#5c5c5c;
            --bg-100:#FFFFFF;
            --bg-200:#f5f5f5;
            --bg-300:#cccccc;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-100);
            color: var(--text-100);
        }
        .glass-card {
            background: var(--bg-200);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(224, 224, 224, 0.2);
        }
        .chart-container {
            height: 300px;
            width: 100%;
            position: relative;
        }
        /* 修复产品反馈分布和功能评价对比图表溢出问题 */
        .glass-card:nth-child(1) .chart-container,
        .glass-card:nth-child(2) .chart-container {
            max-width: 100%;
            overflow: hidden;
        }
        .highlight-blue {
            color: var(--info-color);
            font-weight: 600;
        }
        .highlight-green {
            color: var(--success-color);
            font-weight: 600;
        }
        .highlight-red {
            color: var(--danger-color);
            font-weight: 600;
        }
        .highlight-yellow {
            color: var(--warning-color);
            font-weight: 600;
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        /* 交互相关样式 */
        .detail-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 350px;
            height: 100vh;
            background: var(--bg-100);
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 100;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            padding: 20px;
        }
        .detail-panel.active {
            transform: translateX(0);
        }
        .detail-panel h3 {
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--bg-300);
        }
        .detail-panel .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
        }
        .feedback-item {
            padding: 10px;
            margin-bottom: 10px;
            background: var(--bg-200);
            border-radius: 6px;
            border-left: 3px solid var(--primary-100);
        }
        .chart-tooltip {
            position: absolute;
            padding: 8px 12px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 50;
            max-width: 200px;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .chart-container {
            position: relative;
        }
        .filter-container {
            margin-bottom: 15px;
            text-align: right;
        }
        .filter-container select {
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid var(--bg-300);
            background: var(--bg-100);
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 12px;
        }
        .data-table th, .data-table td {
            border: 1px solid var(--bg-300);
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: var(--bg-200);
        }
        .swiper-slide {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
</head>
<body class="min-h-screen pb-20">
    <!-- 顶部导航区 -->
    <header class="sticky top-0 z-50 bg-[var(--bg-200)] shadow-sm">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-bold text-[var(--text-100)]">
                    <i class="fas fa-comments mr-2"></i>经销商年会工牌分析报告
                </h1>
                <button class="md:hidden text-gray-600">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <!-- 核心指标卡片 -->
            <div class="mt-4 grid grid-cols-3 gap-3">
                <div class="glass-card p-3 text-center">
                    <p class="text-sm text-[var(--text-100)]">话题参与度</p>
                    <p class="text-2xl font-bold text-[var(--accent-200)]"><span id="topicEngagement"></span><span class="text-sm">%</span></p>
                </div>
                <div class="glass-card p-3 text-center">
                    <p class="text-sm text-[var(--text-100)]">平均满意度</p>
                    <p class="text-2xl font-bold text-[var(--accent-200)]"><span id="averageSatisfaction"></span><span class="text-sm">%</span></p>
                </div>
                <div class="glass-card p-3 text-center">
                    <p class="text-sm text-[var(--text-100)]">高频关键词</p>
                    <p class="text-2xl font-bold text-[var(--accent-200)]">+<span id="topKeywordsGrowth"></span><span class="text-sm">%</span></p>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 mt-6 space-y-6">
        <!-- 产品反馈模块 -->
        <section class="glass-card p-4 animate-fade-in">
            <h2 class="text-lg font-bold mb-4 flex items-center">
                <i class="fas fa-box-open text-[var(--primary-100)] mr-2"></i>产品反馈分析
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 情感反馈仪表盘 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">新车情感分布</h3>
                    <div class="chart-container">
                        <canvas id="sentimentChart"></canvas>
                    </div>
                    <div class="mt-2 flex justify-center space-x-4">
                        <span class="text-xs"><i class="fas fa-circle text-[var(--primary-100)] mr-1"></i>正面 <span id="positivePercentage"></span>%</span>
                        <span class="text-xs"><i class="fas fa-circle text-[rgba(31,58,95,0.7)] mr-1"></i>负面 <span id="negativePercentage"></span>%</span>
                        <span class="text-xs"><i class="fas fa-circle text-[rgba(31,58,95,0.4)] mr-1"></i>中性 <span id="neutralPercentage"></span>%</span>
                    </div>
                </div>
                
                <!-- 功能评价雷达图 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">功能评价对比</h3>
                    <div class="filter-container">
                        <select id="modelFilter">
                            <option value="all">所有车型</option>
                            <option value="our">我司产品</option>
                            <option value="competitor1">竞品A</option>
                            <option value="competitor2">竞品B</option>
                            <option value="competitor3">竞品C</option>
                        </select>
                    </div>
                    <div class="chart-container">
                        <canvas id="radarChart"></canvas>
                    </div>
                </div>
                
                <!-- 评价趋势线 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">新车满意度趋势</h3>
                    <div class="chart-container">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- 政策流程模块 -->
        <section class="glass-card p-4 animate-fade-in">
            <h2 class="text-lg font-bold mb-4 flex items-center">
                <i class="fas fa-question-circle text-[var(--primary-100)] mr-2"></i>问题与解答分析
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 问题热力图 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">问题类型分布</h3>
                    <div class="chart-container">
                        <canvas id="issueTypeChart"></canvas>
                    </div>
                </div>
                
                <!-- 高频问题TOP5 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">高频问题TOP5</h3>
                    <div class="chart-container">
                        <canvas id="topIssuesChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 问题解决进度条 -->
            <div class="glass-card p-4 mt-4">
                <h3 class="text-sm font-semibold mb-2">问题解决进度</h3>
                <div class="space-y-2">
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>已解决</span>
                            <span id="solvedPercentage"></span>%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-[var(--primary-200)] h-2 rounded-full" style="width: var(--solved-width)"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>处理中</span>
                            <span id="inProgressPercentage"></span>%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-[rgba(77,100,141,0.7)] h-2 rounded-full" style="width: var(--in-progress-width)"></div>
                        </div>
                    </div>
                    <script>
                        // 问题解决进度条悬停交互
                        document.querySelectorAll('.glass-card:nth-child(3) .space-y-2 > div').forEach(progressBar => {
                            progressBar.addEventListener('mouseenter', function(e) {
                                const status = this.querySelector('span:first-child').textContent;
                                const percentage = this.querySelector('span:last-child').textContent;
                                const tooltip = document.getElementById('customTooltip');
                                
                                // 未解决问题数据
                                // 从数据文件获取未解决问题
                                const unresolvedIssues = reportData.issueAnalysis.resolutionProgress.unresolvedIssues;
                                
                                let content = `<strong>${status}: ${percentage}</strong>`;
                                if (status !== '已解决') {
                                    content += '<br><br>未解决问题:<br>';
                                    content += unresolvedIssues.map(issue => `- ${issue}`).join('<br>');
                                }
                                
                                tooltip.innerHTML = content;
                                tooltip.style.opacity = '1';
                                tooltip.style.left = `${e.pageX + 10}px`;
                                tooltip.style.top = `${e.pageY + 10}px`;
                            });
                            
                            progressBar.addEventListener('mousemove', function(e) {
                                const tooltip = document.getElementById('customTooltip');
                                tooltip.style.left = `${e.pageX + 10}px`;
                                tooltip.style.top = `${e.pageY + 10}px`;
                            });
                            
                            progressBar.addEventListener('mouseleave', function() {
                                const tooltip = document.getElementById('customTooltip');
                                tooltip.style.opacity = '0';
                            });
                        });
                    </script>
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>待处理</span>
                            <span id="pendingPercentage"></span>%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-[rgba(77,100,141,0.4)] h-2 rounded-full" style="width: var(--pending-width)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 反馈主题分析模块 -->
        <section class="glass-card p-4 animate-fade-in">
            <h2 class="text-lg font-bold mb-4 flex items-center">
                <i class="fas fa-tags text-[var(--primary-100)] mr-2"></i>反馈主题分析
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 高频关键词云 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">高频关键词云</h3>
                    <div class="chart-container">
                        <canvas id="wordCloudChart"></canvas>
                    </div>
                </div>
                
                <!-- 主题情感分布 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">主题情感分布</h3>
                    <div class="chart-container">
                        <canvas id="topicSentimentChart"></canvas>
                    </div>
                </div>
                
                <!-- 主题参与度趋势 -->
                <div class="glass-card p-4">
                    <h3 class="text-sm font-semibold mb-2">主题参与度趋势</h3>
                    <div class="filter-container">
                        <select id="topicFilter">
                            <option value="all">所有主题</option>
                            <option value="price">价格</option>
                            <option value="performance">性能</option>
                            <option value="design">设计</option>
                            <option value="service">服务</option>
                        </select>
                    </div>
                    <div class="chart-container">
                        <canvas id="topicTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- 详情面板 -->
    <div class="detail-panel" id="detailPanel">
        <button class="close-btn" id="closeDetailPanel">&times;</button>
        <h3 id="panelTitle"></h3>
        <div id="panelContent"></div>
    </div>
    
    <!-- 自定义提示框 -->
    <div class="chart-tooltip" id="customTooltip"></div>

    <script>
        // 获取CSS变量
        const root = document.documentElement;
        const getCssVar = (variable) => getComputedStyle(root).getPropertyValue(variable).trim();
        
        const primary100 = getCssVar('--primary-100');
        const primary200 = getCssVar('--primary-200');
        const primary300 = getCssVar('--primary-300');
        const accent100 = getCssVar('--accent-100');
        const accent200 = getCssVar('--accent-200');
        const text100 = getCssVar('--text-100');
        const text200 = getCssVar('--text-200');
        const bg100 = getCssVar('--bg-100');
        const bg200 = getCssVar('--bg-200');
        const bg300 = getCssVar('--bg-300');
        const successColor = getCssVar('--success-color');
        const dangerColor = getCssVar('--danger-color');
        const warningColor = getCssVar('--warning-color');
        const infoColor = getCssVar('--info-color');

        // 十六进制转RGB函数
        function hexToRgb(hex) {
            hex = hex.replace('#', '');
            const r = parseInt(hex.substring(0, 2), 16);
            const g = parseInt(hex.substring(2, 4), 16);
            const b = parseInt(hex.substring(4, 6), 16);
            return `${r}, ${g}, ${b}`;
        }
        // 初始化所有图表
        document.addEventListener('DOMContentLoaded', function() {
            // 填充核心指标数据
            document.getElementById('topicEngagement').textContent = reportData.coreIndicators.topicEngagement;
            document.getElementById('averageSatisfaction').textContent = reportData.coreIndicators.averageSatisfaction;
            document.getElementById('topKeywordsGrowth').textContent = reportData.coreIndicators.topKeywordsGrowth;
            
            // 填充情感分布百分比
            document.getElementById('positivePercentage').textContent = reportData.productFeedback.sentimentDistribution.data[0];
            document.getElementById('negativePercentage').textContent = reportData.productFeedback.sentimentDistribution.data[1];
            document.getElementById('neutralPercentage').textContent = reportData.productFeedback.sentimentDistribution.data[2];
            
            // 设置问题解决进度条
            const resolutionProgress = reportData.issueAnalysis.resolutionProgress;
            document.getElementById('solvedPercentage').textContent = resolutionProgress.solved;
            document.getElementById('inProgressPercentage').textContent = resolutionProgress.inProgress;
            document.getElementById('pendingPercentage').textContent = resolutionProgress.pending;
            
            // 设置CSS变量控制进度条宽度
            document.documentElement.style.setProperty('--solved-width', `${resolutionProgress.solved}%`);
            document.documentElement.style.setProperty('--in-progress-width', `${resolutionProgress.inProgress}%`);
            document.documentElement.style.setProperty('--pending-width', `${resolutionProgress.pending}%`);
            // 情感反馈仪表盘
            const sentimentCtx = document.getElementById('sentimentChart').getContext('2d');
            new Chart(sentimentCtx, {
                type: 'doughnut',
                data: {
                    labels: reportData.productFeedback.sentimentDistribution.labels,
                    datasets: [{
                        data: reportData.productFeedback.sentimentDistribution.data,
                        backgroundColor: [
                          primary100,
                          `rgba(${hexToRgb(primary100)}, 0.7)`,
                          `rgba(${hexToRgb(primary100)}, 0.4)`
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const keywords = reportData.productFeedback.sentimentDistribution.keywords;
                                    const keywordList = keywords[label] || [];
                                    return `${label}: ${value}% \n关键词: ${keywordList.join(', ')}`;
                                }
                            }
                        }
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true
                    }
                }
            });

            // 情感分布饼图点击事件
            const sentimentChart = Chart.getChart('sentimentChart');
            sentimentChart.options.onClick = function(e, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const label = this.data.labels[index];
                    const feedbackData = reportData.productFeedback.sentimentDistribution.feedback;

                    showDetailPanel(`${label}情感反馈详情`, feedbackData[label].map(item =>
                        `<div class="feedback-item">${item}</div>`
                    ).join(''));
                }
            };

            // 车型筛选器事件
            document.getElementById('modelFilter').addEventListener('change', function(e) {
                const selectedModel = e.target.value;
                const radarChart = Chart.getChart('radarChart');
                const modelData = {
                    'our': {
                        label: '我司产品',
                        data: [85, 78, 82, 90, 88],
                        borderColor: primary100
                    },
                    'competitor1': {
                        label: '竞品A',
                        data: [80, 85, 75, 78, 82],
                        borderColor: '#FF6384'
                    },
                    'competitor2': {
                        label: '竞品B',
                        data: [75, 70, 80, 85, 78],
                        borderColor: '#36A2EB'
                    },
                    'competitor3': {
                        label: '竞品C',
                        data: [70, 82, 78, 75, 80],
                        borderColor: '#FFCE56'
                    }
                };

                if (selectedModel === 'all') {
                    radarChart.data.datasets = [
                        {
                            label: '我司产品',
                            data: [85, 78, 82, 90, 88],
                            backgroundColor: `rgba(${hexToRgb(primary100)}, 0.2)`,
                            borderColor: primary100,
                            borderWidth: 2,
                            pointBackgroundColor: infoColor,
                            pointRadius: 4
                        }, {
                            label: '行业平均',
                            data: [75, 72, 68, 78, 80],
                            backgroundColor: `rgba(${hexToRgb(text200)}, 0.2)`,
                            borderColor: text200,
                            borderWidth: 2,
                            pointBackgroundColor: '#777',
                            pointRadius: 4
                        }
                    ];
                } else {
                    const model = modelData[selectedModel];
                    radarChart.data.datasets = [
                        {
                            label: model.label,
                            data: model.data,
                            backgroundColor: `rgba(${hexToRgb(model.borderColor)}, 0.2)`,
                            borderColor: model.borderColor,
                            borderWidth: 2,
                            pointBackgroundColor: model.borderColor,
                            pointRadius: 4
                        }, {
                            label: '行业平均',
                            data: [75, 72, 68, 78, 80],
                            backgroundColor: `rgba(${hexToRgb(text200)}, 0.2)`,
                            borderColor: text200,
                            borderWidth: 2,
                            pointBackgroundColor: '#777',
                            pointRadius: 4,
                            borderDash: [5, 5]
                        }
                    ];
                }
                radarChart.update();
            });

            // 详情面板控制函数
            function showDetailPanel(title, content) {
                const panel = document.getElementById('detailPanel');
                const panelTitle = document.getElementById('panelTitle');
                const panelContent = document.getElementById('panelContent');
                
                panelTitle.textContent = title;
                panelContent.innerHTML = content;
                panel.classList.add('active');
            }

            // 关闭详情面板
            document.getElementById('closeDetailPanel').addEventListener('click', function() {
                document.getElementById('detailPanel').classList.remove('active');
            });

            // 功能评价雷达图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: reportData.productFeedback.featureEvaluation.labels,
                    datasets: [{
                        label: reportData.productFeedback.featureEvaluation.datasets.our.label,
                        data: reportData.productFeedback.featureEvaluation.datasets.our.data,
                        backgroundColor: `rgba(${hexToRgb(reportData.productFeedback.featureEvaluation.datasets.our.borderColor)}, 0.2)`,
                        borderColor: reportData.productFeedback.featureEvaluation.datasets.our.borderColor,
                        borderWidth: 2,
                        pointBackgroundColor: infoColor,
                        pointRadius: 4
                    }, {
                        label: reportData.productFeedback.featureEvaluation.datasets.industry.label,
                        data: reportData.productFeedback.featureEvaluation.datasets.industry.data,
                        backgroundColor: `rgba(${hexToRgb(reportData.productFeedback.featureEvaluation.datasets.industry.borderColor)}, 0.2)`,
                        borderColor: reportData.productFeedback.featureEvaluation.datasets.industry.borderColor,
                        borderWidth: 2,
                        pointBackgroundColor: '#777',
                        pointRadius: 4
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 50,
                            suggestedMax: 100
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 评价趋势线
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: reportData.productFeedback.satisfactionTrend.labels,
                    datasets: [{
                        label: '满意度指数',
                        data: reportData.productFeedback.satisfactionTrend.data,
                        backgroundColor: `rgba(${hexToRgb(primary100)}, 0.1)`,
                        borderColor: primary100,
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    scales: {
                        y: {
                            min: 75,
                            max: 95
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // 满意度趋势图点击事件
            const trendChart = Chart.getChart('trendChart');
            trendChart.options.onClick = function(e, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const month = this.data.labels[index];
                    const satisfaction = this.data.datasets[0].data[index];
                    const events = reportData.productFeedback.satisfactionTrend.events;
                    
                    // 模拟数据
                    const feedbackSummary = `
                        <p><strong>时间:</strong> ${month}</p>
                        <p><strong>满意度:</strong> ${satisfaction}</p>
                        <p><strong>关联事件:</strong> ${events[index]}</p>
                        <table class="data-table">
                            <tr><th>反馈类型</th><th>数量</th><th>占比</th></tr>
                            <tr><td>正面评价</td><td>128</td><td>72%</td></tr>
                            <tr><td>中性评价</td><td>32</td><td>18%</td></tr>
                            <tr><td>负面评价</td><td>18</td><td>10%</td></tr>
                        </table>
                    `;

                    showDetailPanel(`${month}满意度详情`, feedbackSummary);
                }
            };

            // 问题类型分布饼图
            const issueTypeCtx = document.getElementById('issueTypeChart').getContext('2d');
            new Chart(issueTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: reportData.issueAnalysis.issueTypeDistribution.labels,
                    datasets: [{
                        data: reportData.issueAnalysis.issueTypeDistribution.data,
                        backgroundColor: [
                            primary100,
                            `rgba(${hexToRgb(primary100)}, 0.8)`,
                            `rgba(${hexToRgb(primary100)}, 0.6)`,
                            `rgba(${hexToRgb(primary100)}, 0.4)`,
                            `rgba(${hexToRgb(primary100)}, 0.2)`
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 问题类型分布饼图点击事件 - 联动更新高频问题图表
            const issueTypeChart = Chart.getChart('issueTypeChart');
            issueTypeChart.options.onClick = function(e, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const issueType = this.data.labels[index];
                    const topIssuesChart = Chart.getChart('topIssuesChart');
                    
                    // 不同问题类型对应的高频问题数据
                    const issueData = reportData.issueAnalysis.topIssues;

                    // 更新高频问题图表
                    if (issueData[issueType]) {
                        topIssuesChart.data.labels = issueData[issueType].labels;
                        topIssuesChart.data.datasets[0].data = issueData[issueType].data;
                        topIssuesChart.update();
                    }
                }
            };

            // 高频问题TOP5
            const topIssuesCtx = document.getElementById('topIssuesChart').getContext('2d');
            new Chart(topIssuesCtx, {
                type: 'bar',
                data: {
                    labels: reportData.issueAnalysis.topIssues.all.labels,
                    datasets: [{
                        label: '问题频次',
                        data: reportData.issueAnalysis.topIssues.all.data,
                        backgroundColor: [
                            `rgba(${hexToRgb(primary100)}, 0.7)`,
                            `rgba(${hexToRgb(accent100)}, 0.7)`,
                            `rgba(${hexToRgb(primary300)}, 0.7)`,
                            `rgba(${hexToRgb(bg300)}, 0.7)`,
                            `rgba(${hexToRgb(accent200)}, 0.7)`
                        ]
                    }]
                },
                options: {
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // 高频问题TOP5点击事件
            const topIssuesChart = Chart.getChart('topIssuesChart');
            topIssuesChart.options.onClick = function(e, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const issue = this.data.labels[index];
                    
                    // 模拟问题反馈数据
                    const issueFeedback = reportData.issueAnalysis.issueFeedback;

                    // 显示问题详情
                    if (issueFeedback[issue]) {
                        showDetailPanel(`${issue}反馈详情`, issueFeedback[issue].map(item =>
                            `<div class="feedback-item">${item}</div>`
                        ).join(''));
                    }
                }
            };

            // 高频关键词云 (使用Chart.js模拟词云效果)
            const wordCloudCtx = document.getElementById('wordCloudChart').getContext('2d');
            new Chart(wordCloudCtx, {
                type: 'bubble',
                data: {
                    datasets: [{
                        label: '关键词',
                        data: reportData.topicAnalysis.keywords,
                        backgroundColor: `rgba(${hexToRgb(primary100)}, 0.7)`
                    }]
                },
                options: {
                    scales: {
                        x: {display: false, min: 0, max: 10},
                        y: {display: false, min: 0, max: 10}
                    },
                    plugins: {
                        legend: {display: false},
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const text = context.raw.text;
                                    const keywordData = reportData.topicAnalysis.keywords.find(k => k.text === text);
                                    const sentiment = keywordData.sentiment;
                                    const frequency = keywordData.frequency;
                                    return `${text}: 频率${frequency[text]}次，情感${sentiment[text]}`;
                                }
                            }
                        }
                    }
                }
            });

            // 主题情感分布堆叠柱状图
            const topicSentimentCtx = document.getElementById('topicSentimentChart').getContext('2d');
            new Chart(topicSentimentCtx, {
                type: 'bar',
                data: {
                    labels: reportData.topicAnalysis.topicSentiment.labels,
                    datasets: reportData.topicAnalysis.topicSentiment.datasets.map((dataset, index) => ({
                        label: dataset.label,
                        data: dataset.data,
                        backgroundColor: index === 0 ? `rgba(${hexToRgb(primary100)}, 0.8)` :
                                         index === 1 ? `rgba(${hexToRgb(primary100)}, 0.4)` :
                                         `rgba(${hexToRgb(primary100)}, 0.2)`
                    }))
                },
                options: {
                    scales: {
                        x: {stacked: true},
                        y: {stacked: true, max: 100}
                    },
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // 主题参与度趋势筛选事件
            document.getElementById('topicFilter').addEventListener('change', function(e) {
                const selectedTopic = e.target.value;
                const topicTrendChart = Chart.getChart('topicTrendChart');
                const originalDatasets = [
                    {
                        label: '价格',
                        data: [12, 19, 15, 28, 35, 30],
                        borderColor: primary100,
                        backgroundColor: `rgba(${hexToRgb(primary100)}, 0.1)`,
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: '性能',
                        data: [8, 15, 22, 20, 25, 28],
                        borderColor: accent100,
                        backgroundColor: `rgba(${hexToRgb(accent100)}, 0.1)`,
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: '设计',
                        data: [5, 12, 18, 15, 10, 15],
                        borderColor: primary300,
                        backgroundColor: `rgba(${hexToRgb(primary300)}, 0.1)`,
                        tension: 0.3,
                        fill: true
                    }
                ];

                if (selectedTopic === 'all') {
                    topicTrendChart.data.datasets = originalDatasets;
                } else {
                    const topicMap = {
                        'price': '价格',
                        'performance': '性能',
                        'design': '设计',
                        'service': '服务'
                    };
                    
                    const topicName = topicMap[selectedTopic] || '';
                    const filteredDataset = originalDatasets.find(dataset => dataset.label === topicName);
                    
                    if (filteredDataset) {
                        topicTrendChart.data.datasets = [filteredDataset];
                    }
                }
                topicTrendChart.update();
            });

            // 关键词云点击事件 - 联动主题情感分布图表
            const wordCloudChart = Chart.getChart('wordCloudChart');
            wordCloudChart.options.onClick = function(e, elements) {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const keyword = this.data.datasets[0].data[index].text;
                    const topicSentimentChart = Chart.getChart('topicSentimentChart');
                    
                    // 关键词与主题的映射关系
                    const keywordToTopic = reportData.topicAnalysis.keywordToTopic;
                    
                    const topic = keywordToTopic[keyword] || '';
                    if (topic) {
                        // 重置所有数据集透明度
                        topicSentimentChart.data.datasets.forEach(dataset => {
                            dataset.backgroundColor = dataset.backgroundColor.replace(/0\.[248]/, '0.2');
                        });
                        
                        // 高亮显示对应主题
                        const topicIndex = topicSentimentChart.data.labels.indexOf(topic);
                        if (topicIndex !== -1) {
                            topicSentimentChart.data.datasets.forEach(dataset => {
                                const bgColor = dataset.backgroundColor;
                                dataset.backgroundColor = bgColor.replace('0.2', '0.8');
                            });
                            topicSentimentChart.update();
                        }
                    }
                }
            };

            // 主题情感分布点击事件
            const topicSentimentChart = Chart.getChart('topicSentimentChart');
            topicSentimentChart.options.onClick = function(e, elements) {
                if (elements.length > 0) {
                    const element = elements[0];
                    const topic = this.data.labels[element.dataIndex];
                    const sentiment = this.data.datasets[element.datasetIndex].label;
                    
                    // 模拟主题情感反馈数据
                    const topicFeedback = reportData.topicAnalysis.topicSentiment.feedback;

                    // 显示主题情感详情
                    if (topicFeedback[topic] && topicFeedback[topic][sentiment]) {
                        showDetailPanel(`${topic}-${sentiment}反馈详情`, topicFeedback[topic][sentiment].map(item =>
                            `<div class="feedback-item">${item}</div>`
                        ).join(''));
                    }
                }
            };

            // 主题参与度趋势折线图
            const topicTrendCtx = document.getElementById('topicTrendChart').getContext('2d');
            new Chart(topicTrendCtx, {
                type: 'line',
                data: {
                    labels: reportData.topicAnalysis.topicTrend.labels,
                    datasets: reportData.topicAnalysis.topicTrend.datasets
                },
                options: {
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: '参与度'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 初始化mermaid图表
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true,
                    curve: 'basis'
                }
            });
        });

        // 滚动动画效果
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, {threshold: 0.1});

        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });
    </script>
</body>
</html>
